// API route for brand-specific generated posts (MongoDB-based)
import { NextRequest, NextResponse } from 'next/server';
import { generatedPostMongoService } from '@/lib/mongodb/services/generated-post-service';
import { createClient } from '@supabase/supabase-js';
import jwt from 'jsonwebtoken';

// Server-side Supabase client for reading posts
const supabaseRead = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

// Helper function to convert MongoDB user ID to Supabase UUID
function convertMongoIdToUuid(mongoUserId: string): string {
  // Map known MongoDB user IDs to their Supabase UUIDs
  const userIdMap: { [key: string]: string } = {
    'user_1756919792493_bvxvnk1hs': '58b4d78d-cb90-49ef-9524-7238aea00168', // Your actual user
    'user_1757090229862_jgzj8xof1': '58b4d78d-cb90-49ef-9524-7238aea00168', // Test user (same UUID)
  };

  return userIdMap[mongoUserId] || '58b4d78d-cb90-49ef-9524-7238aea00168'; // Default to your UUID
}



// GET /api/generated-posts/brand/[brandId] - Get posts for specific brand
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ brandId: string }> }
) {
  try {
    // Extract user ID from JWT token
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Authorization token required' },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7);
    let userId: string;

    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;
      userId = decoded.userId;
    } catch (jwtError) {
      return NextResponse.json(
        { error: 'Invalid token' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '10');
    const { brandId } = await params;

    console.log('🔍 API: Loading posts for brand:', {
      brandId,
      userId,
      limit
    });

    // Convert MongoDB user ID to Supabase UUID
    const supabaseUserId = convertMongoIdToUuid(userId);
    console.log('🔄 API: Converted user ID:', userId, '->', supabaseUserId);

    // Read posts from Supabase for the specific brand
    const { data: posts, error } = await supabaseRead
      .from('generated_posts')
      .select('*')
      .eq('user_id', supabaseUserId)
      .eq('brand_profile_id', brandId)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('❌ Supabase error:', error);
      return NextResponse.json({ error: 'Failed to load brand posts from database' }, { status: 500 });
    }

    console.log('✅ API: Loaded', posts?.length || 0, 'posts for brand from Supabase');

    // Transform Supabase data to frontend format
    const transformedPosts = (posts || []).map(post => ({
      id: post.id,
      userId: userId, // Return original MongoDB user ID for frontend compatibility
      brandProfileId: post.brand_profile_id,
      content: {
        caption: post.content,
        hashtags: post.hashtags,
        imageUrl: post.image_url
      },
      imageUrl: post.image_url,
      platform: post.platform,
      createdAt: post.created_at,
      generationSettings: post.generation_settings
    }));

    return NextResponse.json(transformedPosts);
  } catch (error) {
    console.error('Error loading brand posts:', error);
    return NextResponse.json(
      { error: 'Failed to load brand posts' },
      { status: 500 }
    );
  }
}
