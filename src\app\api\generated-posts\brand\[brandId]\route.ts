// API route for brand-specific generated posts (MongoDB-based)
import { NextRequest, NextResponse } from 'next/server';
import { generatedPostMongoService } from '@/lib/mongodb/services/generated-post-service';
import { createClient } from '@supabase/supabase-js';
import jwt from 'jsonwebtoken';

// Server-side Supabase client for reading posts
const supabaseRead = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

// Helper function to convert MongoDB user ID to Supabase UUID
function convertMongoIdToUuid(mongoUserId: string): string {
  // Map known MongoDB user IDs to their Supabase UUIDs
  const userIdMap: { [key: string]: string } = {
    'user_1756919792493_bvxvnk1hs': '58b4d78d-cb90-49ef-9524-7238aea00168', // Your actual user
    'user_1757090229862_jgzj8xof1': '58b4d78d-cb90-49ef-9524-7238aea00168', // Test user (same UUID)
  };

  return userIdMap[mongoUserId] || '58b4d78d-cb90-49ef-9524-7238aea00168'; // Default to your UUID
}



// GET /api/generated-posts/brand/[brandId] - Get posts for specific brand
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ brandId: string }> }
) {
  try {
    // Extract user ID from JWT token
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Authorization token required' },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7);
    let userId: string;

    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;
      userId = decoded.userId;
    } catch (jwtError) {
      return NextResponse.json(
        { error: 'Invalid token' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '10');
    const { brandId } = await params;

    console.log('🔍 API: Loading posts for brand:', {
      brandId,
      userId,
      limit
    });

    // Load posts from MongoDB using the service method
    const allPosts = await generatedPostMongoService.loadGeneratedPosts(userId, limit * 2);

    // Filter posts by brand ID
    const brandPosts = allPosts.filter(post => post.brandProfileId === brandId);

    // Limit the results
    const posts = brandPosts.slice(0, limit);

    console.log('✅ API: Loaded', posts?.length || 0, 'posts for brand from MongoDB');

    // Posts are already in the correct format from MongoDB service
    const transformedPosts = posts;

    return NextResponse.json(transformedPosts);
  } catch (error) {
    console.error('Error loading brand posts:', error);
    return NextResponse.json(
      { error: 'Failed to load brand posts' },
      { status: 500 }
    );
  }
}
