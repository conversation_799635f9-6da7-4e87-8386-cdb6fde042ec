// API routes for generated posts management
import { NextRequest, NextResponse } from 'next/server';
import { generatedPostMongoService } from '@/lib/mongodb/services/generated-post-service';
import { createClient } from '@supabase/supabase-js';
import jwt from 'jsonwebtoken';

// Server-side Supabase client for reading posts
const supabaseRead = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

// Server-side Supabase client (for image storage only)
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

// MongoDB-based storage with Supabase for images only

// Helper function to convert MongoDB user ID to Supabase UUID
function convertMongoIdToUuid(mongoUserId: string): string {
  // Map known MongoDB user IDs to their Supabase UUIDs
  const userIdMap: { [key: string]: string } = {
    'user_1756919792493_bvxvnk1hs': '58b4d78d-cb90-49ef-9524-7238aea00168', // Your actual user
    'user_1757090229862_jgzj8xof1': '58b4d78d-cb90-49ef-9524-7238aea00168', // Test user (same UUID)
  };

  return userIdMap[mongoUserId] || '58b4d78d-cb90-49ef-9524-7238aea00168'; // Default to your UUID
}

// Helper function to upload data URL to Supabase Storage
async function uploadDataUrlToSupabase(dataUrl: string, userId: string, fileName: string): Promise<{ success: boolean; url?: string; error?: string }> {
  try {
    console.log('📤 Uploading image to Supabase Storage:', fileName);

    // Convert data URL to buffer
    const base64Data = dataUrl.split(',')[1];
    const buffer = Buffer.from(base64Data, 'base64');

    // Upload to Supabase Storage
    const uploadPath = `generated-content/${userId}/${fileName}`;
    const { data, error } = await supabase.storage
      .from('nevis-storage')
      .upload(uploadPath, buffer, {
        contentType: 'image/png',
        upsert: true
      });

    if (error) {
      console.error('❌ Supabase Storage upload error:', error);
      return { success: false, error: error.message };
    }

    // Get public URL
    const { data: { publicUrl } } = supabase.storage
      .from('nevis-storage')
      .getPublicUrl(uploadPath);

    console.log('✅ Image uploaded to Supabase Storage:', publicUrl);
    return { success: true, url: publicUrl };
  } catch (error) {
    console.error('❌ Supabase Storage upload error:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
}

// GET /api/generated-posts - Load user's generated posts
export async function GET(request: NextRequest) {
  try {
    console.log('🔄 API: received GET request to load generated posts');

    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '100');
    const platform = searchParams.get('platform');
    const status = searchParams.get('status');

    // Get authorization header
    const authHeader = request.headers.get('authorization');
    let userId: string;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      console.log('⚠️  API: No auth header found, checking URL parameters...');
      const urlUserId = searchParams.get('userId');
      if (urlUserId) {
        console.log('🔄 API: Using URL userId for development:', urlUserId);
        userId = urlUserId;
      } else {
        return NextResponse.json(
          { error: 'Authorization token or userId parameter required' },
          { status: 401 }
        );
      }
    } else {
      const token = authHeader.substring(7);

      try {
        // Try Supabase authentication first
        const { createClient } = await import('@supabase/supabase-js');
        const supabaseAuth = createClient(
          process.env.NEXT_PUBLIC_SUPABASE_URL!,
          process.env.SUPABASE_SERVICE_ROLE_KEY!
        );
        
        const { data: { user }, error } = await supabaseAuth.auth.getUser(token);
        
        if (error || !user) {
          // Fallback to MongoDB JWT authentication
          try {
            const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;
            userId = decoded.userId;
            console.log('✅ API: MongoDB JWT authentication successful for user:', userId);
          } catch (jwtError) {
            console.log('❌ API: Both Supabase and JWT verification failed');
            const urlUserId = searchParams.get('userId');
            if (urlUserId) {
              console.log('🔄 API: Using URL userId as fallback:', urlUserId);
              userId = urlUserId;
            } else {
              return NextResponse.json(
                { error: 'Invalid token and no userId parameter' },
                { status: 401 }
              );
            }
          }
        } else {
          userId = user.id;
          console.log('✅ API: Supabase authentication successful for user:', user.id);
        }
      } catch (authError) {
        console.log('⚠️  API: Authentication error, using URL fallback...', authError);
        const urlUserId = searchParams.get('userId');
        if (urlUserId) {
          console.log('🔄 API: Using URL userId as fallback:', urlUserId);
          userId = urlUserId;
        } else {
          return NextResponse.json(
            { error: 'Authentication failed and no userId parameter' },
            { status: 401 }
          );
        }
      }
    }

    console.log('🔍 API: Loading posts for user:', userId);

    // Load posts from MongoDB using the service method
    const posts = await generatedPostMongoService.loadGeneratedPosts(userId, limit);

    console.log('✅ API: Loaded', posts?.length || 0, 'posts from MongoDB');

    // Filter by platform if specified
    let filteredPosts = posts;
    if (platform) {
      filteredPosts = posts.filter(post => post.platform === platform);
      console.log('🔍 API: Filtered to', filteredPosts.length, 'posts for platform:', platform);
    }

    // Filter by status if specified
    if (status) {
      filteredPosts = filteredPosts.filter(post => post.status === status);
      console.log('🔍 API: Filtered to', filteredPosts.length, 'posts with status:', status);
    }

    // Posts are already in the correct format from MongoDB service
    const transformedPosts = filteredPosts;

    return NextResponse.json(transformedPosts);
  } catch (error) {
    console.error('Error loading generated posts:', error);
    return NextResponse.json(
      { error: 'Failed to load generated posts' },
      { status: 500 }
    );
  }
}

// POST /api/generated-posts - Create new generated post
export async function POST(request: NextRequest) {
  try {
    console.log('🔄 API: received POST to save generated post');

    // Get request data first to access potential userId parameter
    let requestData;
    try {
      requestData = await request.json();
    } catch (jsonError) {
      console.error('❌ API: Failed to parse JSON:', jsonError);
      return NextResponse.json(
        { error: 'Invalid JSON in request body' },
        { status: 400 }
      );
    }

    // Extract user ID with fallback support
    const authHeader = request.headers.get('authorization');
    let userId: string;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      console.log('⚠️  API: No auth header found for POST request');
      // For POST, we can try to get userId from request body
      if (requestData.userId) {
        console.log('🔄 API: Using userId from request body for development:', requestData.userId);
        userId = requestData.userId;
      } else {
        return NextResponse.json(
          { error: 'Authorization token or userId in request body required' },
          { status: 401 }
        );
      }
    } else {
      const token = authHeader.substring(7);

      try {
        // Try Supabase authentication first
        const { createClient } = await import('@supabase/supabase-js');
        const supabaseAuth = createClient(
          process.env.NEXT_PUBLIC_SUPABASE_URL!,
          process.env.SUPABASE_SERVICE_ROLE_KEY!
        );
        
        const { data: { user }, error } = await supabaseAuth.auth.getUser(token);
        
        if (error || !user) {
          // Fallback to MongoDB JWT authentication
          try {
            const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;
            userId = decoded.userId;
            console.log('✅ API: MongoDB JWT authentication successful for user:', userId);
          } catch (jwtError) {
            console.log('❌ API: Both Supabase and JWT verification failed for POST');
            if (requestData.userId) {
              console.log('🔄 API: Using userId from request body as fallback:', requestData.userId);
              userId = requestData.userId;
            } else {
              return NextResponse.json(
                { error: 'Invalid token and no userId in request body' },
                { status: 401 }
              );
            }
          }
        } else {
          userId = user.id;
          console.log('✅ API: Supabase authentication successful for user:', user.id);
        }
      } catch (authError) {
        console.log('⚠️  API: Authentication error for POST, using request body fallback...', authError);
        if (requestData.userId) {
          console.log('🔄 API: Using userId from request body as fallback:', requestData.userId);
          userId = requestData.userId;
        } else {
          return NextResponse.json(
            { error: 'Authentication failed and no userId in request body' },
            { status: 401 }
          );
        }
      }
    }

    const { post, brandProfileId } = requestData;
    console.log('📝 API: Request data received:', {
      hasPost: !!post,
      userId: userId,
      brandProfileId: brandProfileId,
      postId: post?.id,
      platform: post?.platform
    });

    if (!brandProfileId) {
      console.error('❌ API: Missing required fields:', { userId: !!userId, brandProfileId: !!brandProfileId });
      return NextResponse.json(
        { error: 'Brand profile ID is required' },
        { status: 400 }
      );
    }

    if (!post) {
      console.error('❌ API: Missing post data');
      return NextResponse.json(
        { error: 'Post data is required' },
        { status: 400 }
      );
    }

    // console.debug('API: processing images');

    // Process images with Supabase Storage if they exist
    let processedPost = { ...post };
    if (post.imageUrl && post.imageUrl.startsWith('data:')) {
      console.log('📤 Uploading main image to Supabase Storage');
      const imageResult = await uploadDataUrlToSupabase(
        post.imageUrl,
        userId,
        `post-${post.id || Date.now()}-main.png`
      );

      if (imageResult.success && imageResult.url) {
        processedPost.imageUrl = imageResult.url;
        console.log('✅ Main image uploaded to Supabase Storage');
      } else {
        console.error('❌ Failed to upload main image:', imageResult.error);
      }
    }

    // Process content image if it exists
    if (post.content?.imageUrl && post.content.imageUrl.startsWith('data:')) {
      console.log('📤 Uploading content image to Supabase Storage');
      const contentImageResult = await uploadDataUrlToSupabase(
        post.content.imageUrl,
        userId,
        `post-${post.id || Date.now()}-content.png`
      );

      if (contentImageResult.success && contentImageResult.url) {
        processedPost.content = {
          ...processedPost.content,
          imageUrl: contentImageResult.url
        };
        console.log('✅ Content image uploaded to Supabase Storage');
      } else {
        console.error('❌ Failed to upload content image:', contentImageResult.error);
      }
    }
    // Upload variant images (handles Revo 1.0 which sets images only in variants)
    if (Array.isArray(post.variants) && post.variants.length > 0) {
      const updatedVariants: any[] = [];
      for (let i = 0; i < post.variants.length; i++) {
        const v: any = post.variants[i] || {};
        if (v.imageUrl && typeof v.imageUrl === 'string' && v.imageUrl.startsWith('data:')) {
          const variantResult = await uploadDataUrlToSupabase(
            v.imageUrl,
            userId,
            `post-${post.id || Date.now()}-variant-${i}-${(v.platform || 'instagram').toLowerCase()}.png`
          );
          if (variantResult.success && variantResult.url) {
            updatedVariants.push({ ...v, imageUrl: variantResult.url });
            // If no main imageUrl set yet, use the first variant URL as primary
            if (!processedPost.imageUrl) processedPost.imageUrl = variantResult.url;
          } else {
            console.error('❌ Failed to upload variant image:', variantResult.error);
            updatedVariants.push(v);
          }
        } else {
          updatedVariants.push(v);
        }
      }
      processedPost.variants = updatedVariants;
    }


    console.log('💾 API: saving post to MongoDB');

    // Create MongoDB-compatible post data using the service's expected format
    const mongoPost = {
      id: '', // Will be generated by MongoDB
      userId,
      brandProfileId,
      content: processedPost.content,
      hashtags: processedPost.hashtags || processedPost.content?.hashtags || [],
      platform: processedPost.platform || 'instagram',
      postType: 'post',
      imageUrl: processedPost.imageUrl,
      catchyWords: processedPost.catchyWords,
      subheadline: processedPost.subheadline,
      callToAction: processedPost.callToAction,
      variants: processedPost.variants || [],
      metadata: processedPost.metadata || {},
      status: processedPost.status || 'generated'
    };

    console.log('💾 API: Saving post to MongoDB database...');

    // Save to MongoDB database using the service method
    const savedPostId = await generatedPostMongoService.saveGeneratedPost(mongoPost);

    console.log('✅ API: Post saved successfully to MongoDB:', savedPostId);

    return NextResponse.json({
      success: true,
      id: savedPostId,
      post: {
        ...processedPost,
        id: savedPostId
      }
    });
  } catch (error) {
    console.error('❌ API: Error saving generated post:', error);
    console.error('❌ API: Error details:', {
      message: error.message,
      stack: error.stack,
      name: error.name
    });
    return NextResponse.json(
      { error: `Failed to save generated post: ${error.message}` },
      { status: 500 }
    );
  }
}
